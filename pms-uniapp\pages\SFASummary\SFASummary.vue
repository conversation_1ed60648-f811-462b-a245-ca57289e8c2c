<template>
	<view>
		<!--标题和返回-->
		<cu-custom :bgColor="NavBarColor" isBack>
			<block slot="backText">返回</block>
			<block slot="content">每日情况汇总</block>
			<block slot="right">
				<a @click="screen">
					筛选
				</a>
			</block>
		</cu-custom>

		<u-popup :show="showPopup" mode="top" :closeable="true" @close="closePopup">
			<view style='padding: 5%;'>
				<u-form ref="uForm" style="overflow: scroll;height: 20vh;">
					<u-form-item label="时间">
						<uni-datetime-picker type="date" return-type="string" :showClear='false'
							v-model="queryParam.date" />
					</u-form-item>
					<u-form-item label="姓名">
						<u-input v-model="queryParam.username" />
					</u-form-item>
					<u-form-item label="编码">
						<u-input v-model="queryParam.userCode" />
					</u-form-item>
				</u-form>
			</view>
			<u-button type="primary" @click="loadList">搜索</u-button>
		</u-popup>
		<!--滚动加载列表-->
		<mescroll-body ref="mescrollRef" bottom="88" @init="mescrollInit" :up="upOption" :down="downOption"
			@down="downCallback" @up="upCallback">
			<view class="cu-list menu" style="background-color: #F8F8F8; padding: 10px;">
				<view class="cu-item" v-for="(item, index) in list" :key="index" @click="goDetail(item)">
					<view class="promotion-card">
						<view class="card-header">
							<text class="user-name">{{ item.realname }}-{{ item.username }}</text>
							<text class="user-phone">{{ queryParam.date }}</text>
						</view>

						<view class="card-info">
							<!-- 上班打卡 -->
							<view class="info-row">
								<view class="info-item-single">
									<text class="label">上班打卡</text>
									<view class="tag-container">
										<text class="status-tag"
											:class="item.clockInStatus === '是' ? 'tag-success' : 'tag-danger'">
											{{ item.clockInStatus === '是' ? '是' : '否' }}
										</text>
									</view>
								</view>
							</view>

							<!-- 下班打卡 -->
							<view class="info-row">
								<view class="info-item-single">
									<text class="label">下班打卡</text>
									<view class="tag-container">
										<text class="status-tag"
											:class="item.clockOutStatus === '是' ? 'tag-success' : 'tag-danger'">
											{{ item.clockOutStatus === '是' ? '是' : '否' }}
										</text>
									</view>
								</view>
							</view>

							<!-- 是否有拜访计划 -->
							<view class="info-row">
								<view class="info-item-single">
									<text class="label">是否有拜访计划</text>
									<view class="tag-container">
										<text class="status-tag"
											:class="item.hasPlan === '是' ? 'tag-success' : 'tag-danger'">
											{{ item.hasPlan === '是' ? '是' : '否' }}
										</text>
									</view>
								</view>
							</view>

							<!-- 是否有拜访记录 -->
							<view class="info-row">
								<view class="info-item-single">
									<text class="label">是否有拜访记录</text>
									<view class="tag-container">
										<text class="status-tag"
											:class="item.hasRecord === '是' ? 'tag-success' : 'tag-danger'">
											{{ item.hasRecord === '是' ? '是' : '否' }}
										</text>
									</view>
								</view>
							</view>

							<!-- 是否填写日报 -->
							<view class="info-row">
								<view class="info-item-single">
									<text class="label">是否填写日报</text>
									<view class="tag-container">
										<text class="status-tag"
											:class="item.reportStatus === '是' ? 'tag-success' : 'tag-danger'">
											{{ item.reportStatus === '是' ? '是' : '否' }}
										</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view v-if="list.length == 0" class="no-data">
					<text class="no-data-text">
						暂无数据
					</text>
				</view>
			</view>
		</mescroll-body>
	</view>
</template>

<script>
import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
import Mixin from "@/common/mixin/SFASummaryMixin.js";
import dayjs from 'dayjs'

export default {
	name: 'opocUserList',
	mixins: [MescrollMixin, Mixin],
	data() {
		return {
			CustomBar: this.CustomBar,
			NavBarColor: this.NavBarColor,
			url: "/pm/clockIn/overviewList",
			queryParam: {
				date: dayjs().format('YYYY-MM-DD'),
			}
		};
	},
	created() {
	},
	methods: {
		async goDetail(item) {
			// uni.navigateTo({
			// 	url: `/pages/SFASummary/SFASummaryDetail?userCode=${item.username}&date=${this.queryParam.date}&name=${item.realname}`
			// });
			this.$Router.push({
				path: "/pages/SFASummary/SFASummaryDetail",
				query: {
					userCode: item.username,
					date: this.queryParam.date,
					name: item.realname
				},
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.promotion-card {
	background-color: #FFFFFF;
	border-radius: 12px;
	padding: 16px;
	margin-bottom: 15px;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
	position: relative;
	overflow: hidden;
	min-height: 200px;
	display: flex;
	flex-direction: column;
	width: 100%;

	&::before {
		content: "";
		position: absolute;
		top: 0;
		left: 0;
		width: 6px;
		height: 100%;
		background: linear-gradient(to bottom, #2979ff, #56ccf2);
	}
}

.card-header {
	padding-bottom: 12px;
	margin-bottom: 8px;
	border-bottom: 1px solid #F0F0F0;
	display: flex;
	justify-content: space-between;
	align-items: center;
	flex-wrap: wrap;
}

.user-name {
	font-size: 18px;
	font-weight: 600;
	color: #333333;
	margin-right: 10px;
}

.user-phone {
	font-size: 14px;
	color: #666666;
}

.card-info {
	padding: 0 0 12px 0;
	flex: 1;
}

.info-row {
	display: flex;
	margin-bottom: 8px;
	flex-wrap: wrap;
}

.info-item {
	flex: 1;
	min-width: 40%;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	margin-right: 15px;
	padding: 8px 0;

	&.full-width {
		flex: 0 0 100%;
		margin-right: 0;
	}
}

.info-item-single {
	width: 100%;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	padding: 12px 16px;
	background-color: #f8f9fa;
	border-radius: 12px;
	// border-left: 4px solid #007AFF;
	margin-bottom: 4px;
	transition: all 0.3s ease;

	&:active {
		background-color: #e9ecef;
		transform: scale(0.98);
	}
}

.label {
	font-size: 14px;
	color: #333333;
	white-space: nowrap;
	flex-shrink: 0;
	margin-right: 12px;
	font-weight: 500;
	text-align: left;
}

.value {
	font-size: 14px;
	color: #333333;
	word-break: break-word;
	line-height: 1.4;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
}

.card-footer {
	padding-top: 10px;
	border-top: 1px solid #F0F0F0;
	margin-top: auto;
}

.create-time {
	font-size: 12px;
	color: #999999;
}

.no-data {
	text-align: center;
	padding: 20px;
}

.tag-container {
	flex-shrink: 0;
}

.status-tag {
	display: inline-block;
	padding: 6px 16px;
	border-radius: 20px;
	font-size: 13px;
	font-weight: 600;
	text-align: center;
	min-width: 40px;
	line-height: 1.2;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
}

.tag-success {
	background: linear-gradient(135deg, #52c41a, #73d13d);
	color: #ffffff;
	border: none;
}

.tag-danger {
	background: linear-gradient(135deg, #ff4d4f, #ff7875);
	color: #ffffff;
	border: none;
}

::v-deep .uni-date-editor--x .uni-date__icon-clear {
	display: none;
}
</style>
